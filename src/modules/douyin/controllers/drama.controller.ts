import { Controller, Get, Query, Param } from '@nestjs/common'
import { ApiOperation, ApiResponse, ApiTags, ApiParam } from '@nestjs/swagger'
import { CommonResponse } from '@/common/responses/common.response'
import { DouyinDramaService } from '../services/drama.service'
import { DramaRankingRequest, DramaListRequest } from '../requests/douyin.request'
import { DramaRankingResponse, DramaListResponse, DramaDetailResponse } from '../responses/drama.response'

@ApiTags('抖音小程序-短剧')
@Controller('/dy/drama')
export class DouyinDramaController {
  constructor(private readonly douyinDramaService: DouyinDramaService) {}

  @ApiOperation({
    summary: '获取短剧排行榜',
    description: '获取短剧排行榜，支持热播榜（近3个月销量）和必看榜（历史销量）',
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: DramaRankingResponse,
    isArray: true,
  })
  @Get('/ranking')
  async getDramaRanking(@Query() request: DramaRankingRequest) {
    const result = await this.douyinDramaService.getDramaRanking(request)
    return CommonResponse.ok(result)
  }

  @ApiOperation({
    summary: '获取短剧列表',
    description: '根据分类、标题、状态等条件查询短剧列表，支持分页和排序',
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: DramaListResponse,
    isArray: true,
  })
  @Get('/list')
  async getDramaList(@Query() request: DramaListRequest) {
    const result = await this.douyinDramaService.getDramaList(request)
    return CommonResponse.ok(result)
  }

  @ApiOperation({
    summary: '获取短剧详情',
    description: '根据短剧ID获取短剧详细信息，包含剧集列表',
  })
  @ApiParam({
    name: 'id',
    description: '短剧ID',
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: DramaDetailResponse,
  })
  @Get('/:id')
  async getDramaDetail(@Param('id') id: string) {
    const result = await this.douyinDramaService.getDramaDetail(id)
    return CommonResponse.ok(result)
  }
}
